/*
 * @Author: root <EMAIL>
 * @Date: 2025-07-30 14:03:41
 * @LastEditors: 47844 <EMAIL>
 * @LastEditTime: 2025-07-31 15:10:07
 * @FilePath: \institute-front\packages\key-hmes-front\src\routes\ERPFileTransfer\index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState, useEffect, useCallback } from 'react';
import { DataSet, Table, Tabs, Button, notification } from 'choerodon-ui/pro';
import { Upload } from 'hzero-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Header, Content } from 'components/Page';
import { TableQueryBarType, ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { getAccessToken } from 'utils/utils';
import intl from 'utils/intl';
import request from 'utils/request';
import {
  workOrderResultDS,
  outsourceResultDS,
  projectNumResultDS,
  materialResultDS,
  drawingResultDS,
  materialLotResultDS,
} from './stores/tableDS';
import {
  queryValueSet,
  uploadInterfaceFile,
  uploadMaterialLotFile,
  exportInterfaceData,
} from './services';

const { TabPane } = Tabs;
const modelPrompt = 'hmes.erpFileTransfer';

interface InterfaceConfig {
  value: string;
  meaning: string;
  tag?: string;
}

const ERPFileTransfer = () => {
  const [activeMainTab, setActiveMainTab] = useState('import');
  const [activeSubTab, setActiveSubTab] = useState('');
  const [interfaceConfigs, setInterfaceConfigs] = useState<InterfaceConfig[]>([]);
  const [importBatchId, setImportBatchId] = useState<string>('');
  const [uploading, setUploading] = useState(false);

  const [currentResultDS, setCurrentResultDS] = useState<DataSet | null>(null);

  const interfaceTypeMapping = {
    erpWoBomRouter: 'workOrder', // 工单接口
    outsourceCheck: 'outsource', // 外协回传接口
    projectNum: 'projectNum', // 项目令号接口
    materialCode: 'material', // 物料接口
    figureNumberMaterial: 'drawing', // 图号接口
    erpMaterialLot: 'materialLot', // 外购库存接口
  };

  const createResultDataSet = useCallback((interfaceType: string, batchId: string) => {
    const mappedType =
      interfaceTypeMapping[interfaceType as keyof typeof interfaceTypeMapping] || interfaceType;
    try {
      let resultDS: DataSet | null = null;
    
      console.log('mappedType', mappedType);
      
      switch (mappedType) {
        case 'workOrder':
          resultDS = new DataSet(workOrderResultDS());
          break;
        case 'outsource':
          resultDS = new DataSet(outsourceResultDS());
          break;
        case 'projectNum':
          resultDS = new DataSet(projectNumResultDS());
          break;
        case 'material':
          resultDS = new DataSet(materialResultDS());
          break;
        case 'drawing':
          resultDS = new DataSet(drawingResultDS());
          break;
        case 'materialLot':
          resultDS = new DataSet(materialLotResultDS());
          break;
        default:
          return null;
      }

      if (resultDS) {
        resultDS.setQueryParameter('batchId', batchId);
      }

      return resultDS;
    } catch (error) {
      console.error('Error creating DataSet:', error);
      return null;
    }
  }, []);

  // 获取值集配置
  useEffect(() => {
    const fetchValueSet = async () => {
      try {
        const valueSetConfig = queryValueSet('HME.MES_ERP_INTERFACE') as any;
        const response = await request(valueSetConfig.url, {
          method: valueSetConfig.method,
          params: valueSetConfig.params,
        });
        if (response && response.length > 0) {
          setInterfaceConfigs(response);
          // 设置默认的子Tab页
          const importConfigs = response.filter((item: InterfaceConfig) => item.tag === 'IMPORT');
          if (importConfigs.length > 0) {
            setActiveSubTab(importConfigs[0].value);
          }
        }
      } catch (error) {
        console.error('Failed to fetch value set:', error);
      }
    };

    fetchValueSet();
  }, []);

  // 获取当前显示的接口配置
  const getCurrentConfigs = useCallback(() => {
    const tag = activeMainTab === 'import' ? 'IMPORT' : 'EXPORT';
    return interfaceConfigs.filter(config => config.tag === tag);
  }, [interfaceConfigs, activeMainTab]);

  // 主Tab页切换
  const handleMainTabChange = useCallback(
    (key: string) => {
      setActiveMainTab(key);
      const configs = interfaceConfigs.filter(
        config => config.tag === (key === 'import' ? 'IMPORT' : 'EXPORT'),
      );
      if (configs.length > 0) {
        setActiveSubTab(configs[0].value);
      }
      setImportBatchId(''); // 清空批次ID
      setCurrentResultDS(null); // 清空结果数据源
    },
    [interfaceConfigs],
  );

  // 子Tab页切换
  const handleSubTabChange = useCallback((key: string) => {
    setActiveSubTab(key);
    setImportBatchId(''); // 清空批次ID
    setCurrentResultDS(null); // 清空结果数据源
  }, []);

  // 文件上传前验证
  const beforeUpload = useCallback((file: any) => {
    const isJSON = file.type === 'application/json' || file.name.endsWith('.json');
    if (!isJSON) {
      notification.error({
        message: intl.get(`${modelPrompt}.notification.fileTypeError`).d('只能上传JSON格式文件'),
        description: '',
      });
      return false;
    }
    return true;
  }, []);

  // 文件上传成功处理
  const handleUploadSuccess = useCallback(
    (res: any) => {
      setUploading(false);

      if (res && res.success) {
        // 尝试从不同路径获取batchId
        const batchId = res?.rows;

        if (batchId) {
          setImportBatchId(batchId);

          const resultDS = createResultDataSet(activeSubTab, batchId);

          if (resultDS) {
            setCurrentResultDS(resultDS);

            resultDS.addEventListener('loadFailed', (error: any) => {
              console.error('DataSet load failed:', error);
            });

            resultDS.query();
          }

          notification.success({
            message: intl.get(`${modelPrompt}.notification.uploadSuccess`).d('文件上传成功'),
            description: '',
          });
        }
      }
    },
    [activeSubTab, createResultDataSet],
  );

  // 获取上传接口URL
  const getUploadUrl = useCallback(() => {
    if (activeSubTab === 'erpMaterialLot') { // 改为实际的值
      const materialLotConfig = uploadMaterialLotFile(activeSubTab);
      return (materialLotConfig as any).url;
    }
    const interfaceConfig = uploadInterfaceFile(activeSubTab);
    return (interfaceConfig as any).url;
  }, [activeSubTab]);

  // 自定义上传请求
  const customRequest = useCallback(
    ({ file, onProgress, onSuccess }: any) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('interfaceCode', activeSubTab);

      const uploadUrl = getUploadUrl();

      request(uploadUrl, {
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
        },
        onProgress: onProgress ? (e: any) => onProgress(e, file) : null,
      }).then(response => {
        if (response && response.success) {
          onSuccess(response, file);
          handleUploadSuccess(response);
        } else {
          notification.error({ message: response.message, description: '' });
          setUploading(false);
        }
      });
    },
    [activeSubTab, getUploadUrl, handleUploadSuccess],
  );

  // 渲染结果表格
  const renderResultTable = useCallback(() => {
    // 只有在有数据源且有batchId时才显示表格
    if (!currentResultDS || !importBatchId) {
      return null;
    }

    return (
      <Table
        dataSet={currentResultDS}
        queryBar={TableQueryBarType.filterBar}
        queryBarProps={{
          fuzzyQuery: false,
        }}
        columns={getColumnsForInterface(activeSubTab)}
      />
    );
  }, [currentResultDS, activeSubTab, importBatchId]);

  // 根据接口类型获取表格列配置 - 根据接口文档适配字段名
  const getColumnsForInterface = useCallback((interfaceType: string) => {
    // 将值集中的interfaceType映射到内部类型
    const mappedType =
      interfaceTypeMapping[interfaceType as keyof typeof interfaceTypeMapping] || interfaceType;

    const commonColumns = [
      {
        name: 'status',
        width: 120,
        align: ColumnAlign.center,
      },
      {
        name: 'message',
        width: 200,
      },
    ];

    switch (mappedType) {
      case 'workOrder':
        return [
          { name: 'erpUekId', width: 150, align: ColumnAlign.center },
          { name: 'projectNum', width: 120, align: ColumnAlign.center },
          { name: 'materialCode', width: 150, align: ColumnAlign.center },
          { name: 'materialName', width: 200 },
          { name: 'orderNum', width: 120, align: ColumnAlign.center },
          { name: 'qty', width: 100, align: ColumnAlign.right },
          { name: 'planner', width: 120 },
          { name: 'planEndTime', width: 150, align: ColumnAlign.center },
          ...commonColumns,
        ];
      case 'outsource':
        return [
          { name: 'erpUekId', width: 150, align: ColumnAlign.center },
          { name: 'instructionDocNum', width: 150, align: ColumnAlign.center },
          { name: 'projectNum', width: 120, align: ColumnAlign.center },
          { name: 'materialCode', width: 150, align: ColumnAlign.center },
          { name: 'materialName', width: 200 },
          { name: 'qty', width: 100, align: ColumnAlign.right },
          { name: 'checkResult', width: 120, align: ColumnAlign.center },
          { name: 'inspectTime', width: 150, align: ColumnAlign.center },
          ...commonColumns,
        ];
      case 'projectNum':
        return [
          { name: 'projectNum', width: 120, align: ColumnAlign.center },
          { name: 'description', width: 200 },
          { name: 'projectType', width: 120, align: ColumnAlign.center },
          { name: 'projectTypeName', width: 150 },
          { name: 'managerName', width: 120 },
          { name: 'techName', width: 120 },
          { name: 'editedTime', width: 150, align: ColumnAlign.center },
          ...commonColumns,
        ];
      case 'material':
        return [
          { name: 'plantCode', width: 120, align: ColumnAlign.center },
          { name: 'itemCode', width: 150, align: ColumnAlign.center },
          { name: 'descriptions', width: 200 },
          { name: 'primaryUom', width: 100, align: ColumnAlign.center },
          { name: 'model', width: 150 },
          ...commonColumns,
        ];
      case 'drawing':
        return [
          { name: 'plantCode', width: 100, align: ColumnAlign.center },
          { name: 'itemCode', width: 150, align: ColumnAlign.center },
          { name: 'descriptions', width: 200 },
          ...commonColumns,
        ];
      case 'materialLot':
        return [
          { name: 'erpUekId', width: 150, align: ColumnAlign.center },
          { name: 'affiliatedCampus', width: 120, align: ColumnAlign.center },
          { name: 'projectNum', width: 120, align: ColumnAlign.center },
          { name: 'materialCode', width: 150, align: ColumnAlign.center },
          { name: 'materialName', width: 200 },
          { name: 'qty', width: 100, align: ColumnAlign.right },
          { name: 'uomCode', width: 100, align: ColumnAlign.center },
          { name: 'size', width: 150 },
          ...commonColumns,
        ];
      default:
        return commonColumns;
    }
  }, []);

  // 处理导出数据
  const handleExportData = useCallback(async () => {
    try {
      const exportConfig = exportInterfaceData(activeSubTab) as any;
      const response = await request(exportConfig.url, {
        method: exportConfig.method,
        params: exportConfig.params,
      });
      if (response) {
        // 创建下载链接
        const blob = new Blob([JSON.stringify(response, null, 2)], {
          type: 'application/json',
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${activeSubTab}_export_${new Date().getTime()}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        notification.success({
          message: intl.get(`${modelPrompt}.notification.exportSuccess`).d('导出成功'),
          description: '',
        });
      }
    } catch (error) {
      notification.error({
        message: intl.get(`${modelPrompt}.notification.exportError`).d('导出失败'),
        description:
          (error as any)?.message ||
          intl.get(`${modelPrompt}.notification.exportErrorDesc`).d('请检查网络连接'),
      });
    }
  }, [activeSubTab]);

  return (
    <div className="hmes-style">
      <Header title={intl.get(`${modelPrompt}.title`).d('ERP文件传输')} />
      <Content>
        <Tabs activeKey={activeMainTab} onChange={handleMainTabChange}>
          <TabPane tab={intl.get(`${modelPrompt}.tab.import`).d('导入')} key="import">
            {getCurrentConfigs().length > 0 && (
              <Tabs activeKey={activeSubTab} onChange={handleSubTabChange}>
                {getCurrentConfigs().map(config => (
                  <TabPane tab={config.meaning} key={config.value}>
                    <div style={{ marginBottom: 16 }}>
                      <Upload
                        accept=".json"
                        beforeUpload={beforeUpload}
                        customRequest={customRequest}
                        onChange={(info: any) => {
                          if (info.file.status === 'uploading') {
                            setUploading(true);
                          }
                        }}
                        showUploadList={false}
                      >
                        <Button loading={uploading} disabled={uploading}>
                          {intl.get(`${modelPrompt}.button.selectFile`).d('选择文件')}
                        </Button>
                      </Upload>
                    </div>

                    {importBatchId && currentResultDS && <div>{renderResultTable()}</div>}
                  </TabPane>
                ))}
              </Tabs>
            )}
          </TabPane>

          <TabPane tab={intl.get(`${modelPrompt}.tab.export`).d('导出')} key="export">
            {getCurrentConfigs().length > 0 && (
              <Tabs activeKey={activeSubTab} onChange={handleSubTabChange}>
                {getCurrentConfigs().map(config => (
                  <TabPane tab={config.meaning} key={config.value}>
                    <div style={{ marginBottom: 16 }}>
                      <Button color={ButtonColor.primary} onClick={handleExportData}>
                        {intl.get(`${modelPrompt}.button.exportData`).d('导出数据')}
                      </Button>
                    </div>
                  </TabPane>
                ))}
              </Tabs>
            )}
          </TabPane>
        </Tabs>
      </Content>
    </div>
  );
};

export default ERPFileTransfer;
