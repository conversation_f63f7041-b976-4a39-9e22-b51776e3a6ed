/**
 * @Description: ERP文件传输数据源配置
 * @Author: <<EMAIL>>
 * @Date: 2023-08-02 13:34:38
 * @LastEditTime: 2025-07-30 14:28:44
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';

const tenantId = getCurrentOrganizationId();

// 路由配置
const ROUTES = {
  INSTITUTE_TZNI: '/institute-tzni', // 工单、外协、项目令号、物料、图号接口
  INSTITUTE_MES: BASIC.HMES_BASIC,   // 外购库存接口
};


console.log(ROUTES);

const modelPrompt = 'hmes.erpFileTransfer';

// 工单接口结果数据源
const workOrderResultDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'erpUekId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.erpUekId`).d('ERP唯一标识'),
    },
    {
      name: 'projectNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectNum`).d('工作令号'),
    },
    {
      name: 'importBatch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.importBatch`).d('导入批次'),
    },
    {
      name: 'importDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.importDate`).d('导入日期'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('零部件图号'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('零部件名称'),
    },
    {
      name: 'orderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNum`).d('生产令号'),
    },
    {
      name: 'attribute',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attribute`).d('属性'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('需求数量'),
    },
    {
      name: 'parentMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.parentMaterialCode`).d('装入整件代号'),
    },
    {
      name: 'topMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topMaterialCode`).d('总整件代号'),
    },
    {
      name: 'planner',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planner`).d('生产计划员'),
    },
    {
      name: 'planEndTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.planEndTime`).d('要求完成时间'),
    },
    {
      name: 'reverseNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.reverseNum`).d('委托单号'),
    },
    {
      name: 'logic',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.logic`).d('逻辑号值'),
    },
    {
      name: 'useFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.useFlag`).d('使用标识'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注说明'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('数据处理状态'),
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('数据处理消息返回'),
    },
  ],
  transport: {
    read: () => ({
      url: `${ROUTES.INSTITUTE_TZNI}/v1/${tenantId}/hme-part-requirement-task-ifaces/batch/id/list/ui`,
      method: 'GET',
    }),
  },
});

// 外协回传接口结果数据源
const outsourceResultDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'erpUekId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.erpUekId`).d('ERP唯一标识'),
    },
    {
      name: 'instructionDocNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.instructionDocNum`).d('MES系统唯一标识'),
    },
    {
      name: 'projectNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectNum`).d('工作令号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('零部件图号'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('零部件名称'),
    },
    {
      name: 'orderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.orderNum`).d('生产令号'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('外协数量'),
    },
    {
      name: 'parentMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.parentMaterialCode`).d('整件代号'),
    },
    {
      name: 'topMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.topMaterialCode`).d('装入整件代号'),
    },
    {
      name: 'editedNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedNum`).d('编制人员'),
    },
    {
      name: 'editedTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.editedTime`).d('编制日期'),
    },
    {
      name: 'operationType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.operationType`).d('是否表面处理'),
    },
    {
      name: 'startTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.startTime`).d('要求开始时间'),
    },
    {
      name: 'endTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.endTime`).d('要求完成时间'),
    },
    {
      name: 'remark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.remark`).d('备注说明'),
    },
    {
      name: 'passQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.passQty`).d('合格数量'),
    },
    {
      name: 'inspectNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectNum`).d('检验人员编号'),
    },
    {
      name: 'inspectName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.inspectName`).d('检验人员名称'),
    },
    {
      name: 'inspectTime',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.inspectTime`).d('检验时间'),
    },
    {
      name: 'checkResult',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.checkResult`).d('审核状态'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('数据处理状态'),
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('数据处理消息返回'),
    },
  ],
  transport: {
    read: () => ({
      url: `${ROUTES.INSTITUTE_TZNI}/v1/${tenantId}/hme-outsource-check-ifaces/batch/id/list/ui`,
      method: 'GET',
    }),
  },
});

// 项目令号接口结果数据源
const projectNumResultDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'projectNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectNum`).d('工作令号'),
    },
    {
      name: 'description',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.description`).d('令号名称'),
    },
    {
      name: 'projectType',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectType`).d('令号类别'),
    },
    {
      name: 'projectTypeName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectTypeName`).d('类别名称'),
    },
    {
      name: 'managerNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.managerNum`).d('项目主管编号'),
    },
    {
      name: 'managerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.managerName`).d('项目主管名称'),
    },
    {
      name: 'techNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.techNum`).d('项目技术负责人编号'),
    },
    {
      name: 'techName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.techName`).d('项目技术负责人名称'),
    },
    {
      name: 'approve',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.approve`).d('审批流程'),
    },
    {
      name: 'editedNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedNum`).d('编制人员编号'),
    },
    {
      name: 'editedName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedName`).d('编制人员名称'),
    },
    {
      name: 'editedTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.editedTime`).d('编制时间'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('数据处理状态'),
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('数据处理消息返回'),
    },
  ],
  transport: {
    read: () => ({
      url: `${ROUTES.INSTITUTE_TZNI}/v1/${tenantId}/hme-project-num-ifaces/batch/id/list/ui`,
      method: 'GET',
    }),
  },
});

// 物料接口结果数据源
const materialResultDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'plantCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.plantCode`).d('工厂'),
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCode`).d('物资编号'),
    },
    {
      name: 'descriptions',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.descriptions`).d('物资名称'),
    },
    {
      name: 'primaryUom',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.primaryUom`).d('计量单位'),
    },
    {
      name: 'model',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.model`).d('规格型号'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('数据处理状态'),
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('数据处理消息返回'),
    },
  ],
  transport: {
    read: () => ({
      url: `${ROUTES.INSTITUTE_TZNI}/v1/${tenantId}/hme-erp-system-interface/batch/id/list/ui`,
      method: 'GET',
    }),
  },
});

// 图号接口结果数据源
const drawingResultDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'plantCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.plantCode`).d('工厂'),
    },
    {
      name: 'itemCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.itemCode`).d('图号'),
    },
    {
      name: 'descriptions',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.descriptions`).d('图号名称'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('数据处理状态'),
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('数据处理消息返回'),
    },
  ],
  transport: {
    read: () => ({
      url: `${ROUTES.INSTITUTE_TZNI}/v1/${tenantId}/hme-erp-system-interface/batch/id/list/ui`,
      method: 'GET',
    }),
  },
});

// 外购库存接口结果数据源
const materialLotResultDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: false,
  selection: false,
  paging: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'erpUekId',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.erpUekId`).d('ERP唯一标识'),
    },
    {
      name: 'affiliatedCampus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.affiliatedCampus`).d('所属院区'),
    },
    {
      name: 'projectNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.projectNum`).d('工作令号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物资编号'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物资名称'),
    },
    {
      name: 'size',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.size`).d('规格型号'),
    },
    {
      name: 'uomCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.uomCode`).d('单位'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('变更数量'),
    },
    {
      name: 'status',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.status`).d('数据处理状态'),
    },
    {
      name: 'message',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.message`).d('数据处理消息返回'),
    },
  ],
  transport: {
    read: () => ({
      url: `${ROUTES.INSTITUTE_MES}/v1/${tenantId}/hme-erp-system-interface/interface/data/upload/ui`,
      method: 'GET',
    }),
  },
});

export {
  workOrderResultDS,
  outsourceResultDS,
  projectNumResultDS,
  materialResultDS,
  drawingResultDS,
  materialLotResultDS,
};
